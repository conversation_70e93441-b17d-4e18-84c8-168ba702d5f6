# Strategy Sharing Implementation

## Tổng Quan

Thêm tính năng chia sẻ Strategy cho người mua sản phẩm trong marketplace. Khi người dùng mua một sản phẩm có category là `STRATEGY`, hệ thống sẽ tự động tạo bản ghi trong bảng `agents_strategy_user` với example được copy từ `exampleDefault` của strategy gốc.

## Implementation Details

### 1. Files Modified

#### ResourceSharingService
**File**: `src/modules/marketplace/shares/resource-sharing.service.ts`

**Changes**:
- Thêm imports cho `AgentStrategyRepository`, `AgentStrategyUserRepository`, `AgentStrategyUser`
- Thêm case `ProductCategory.STRATEGY` trong `shareResourceForProduct()`
- Thêm case `ProductCategory.STRATEGY` trong `canResourceBePurchased()`
- Thêm method `shareStrategy()`
- Thêm method `checkStrategyCanBePurchased()`

#### MarketplaceUserModule
**File**: `src/modules/marketplace/user/marketplace-user.module.ts`

**Changes**:
- Thêm imports cho `AgentStrategyRepository`, `AgentStrategyUserRepository`
- Thêm entities `AgentStrategy`, `AgentStrategyUser` vào TypeOrmModule
- Thêm repositories vào providers

### 2. New Methods

#### shareStrategy()
```typescript
private async shareStrategy(userId: number, sourceStrategyId: string): Promise<void>
```

**Functionality**:
1. Lấy thông tin strategy gốc với `exampleDefault`
2. Kiểm tra user đã có strategy này chưa
3. Tạo bản ghi mới trong `agents_strategy_user` với:
   - `agentsStrategyId`: ID strategy gốc
   - `userId`: ID người mua
   - `example`: Copy từ `exampleDefault` của strategy gốc
   - `ownedAt`: Timestamp hiện tại
   - `userModelId`: null (user có thể cấu hình sau)
   - `keyLlmId`: null (user có thể cấu hình sau)

#### checkStrategyCanBePurchased()
```typescript
private async checkStrategyCanBePurchased(sourceId: string): Promise<boolean>
```

**Functionality**:
- Kiểm tra strategy có tồn tại và không bị xóa (`deletedBy = null`)
- Return `true` nếu có thể mua, `false` nếu không

### 3. Database Schema

#### agents_strategy_user Table
```sql
CREATE TABLE agents_strategy_user (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agents_strategy_id UUID REFERENCES agents_strategy(id),
  example JSONB DEFAULT '[]',
  user_id INTEGER REFERENCES users(id),
  owned_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000)::bigint,
  user_model_id UUID REFERENCES user_models(id),
  key_llm_id UUID REFERENCES user_key_llm(id)
);
```

### 4. Flow Diagram

```mermaid
graph TD
    A[User mua Strategy Product] --> B[PaymentService.processPayment]
    B --> C[ResourceSharingService.shareResourcesAfterPurchase]
    C --> D[shareResourceForProduct]
    D --> E{Product Category?}
    E -->|STRATEGY| F[shareStrategy]
    F --> G[Lấy strategy gốc với exampleDefault]
    G --> H{Strategy tồn tại?}
    H -->|No| I[Throw RESOURCE_NOT_FOUND]
    H -->|Yes| J{User đã có strategy?}
    J -->|Yes| K[Log warning và return]
    J -->|No| L[Tạo AgentStrategyUser mới]
    L --> M[Copy exampleDefault sang example]
    M --> N[Save vào database]
    N --> O[Log success]
```

### 5. Example Data Flow

#### Input Product
```json
{
  "id": 123,
  "name": "Advanced Sales Strategy",
  "category": "STRATEGY",
  "sourceId": "strategy-uuid-123",
  "userId": null,
  "employeeId": 456
}
```

#### Strategy Source Data
```json
{
  "strategy": {
    "id": "strategy-uuid-123",
    "content": [...],
    "exampleDefault": [
      {
        "stepOrder": 1,
        "content": "Khi khách hàng hỏi về giá: Hãy giải thích giá trị sản phẩm trước"
      },
      {
        "stepOrder": 2,
        "content": "Khi khách hàng do dự: Đưa ra ưu đãi phù hợp"
      }
    ],
    "deletedBy": null
  }
}
```

#### Created AgentStrategyUser
```json
{
  "id": "new-uuid-456",
  "agentsStrategyId": "strategy-uuid-123",
  "userId": 789,
  "example": [
    {
      "stepOrder": 1,
      "content": "Khi khách hàng hỏi về giá: Hãy giải thích giá trị sản phẩm trước"
    },
    {
      "stepOrder": 2,
      "content": "Khi khách hàng do dự: Đưa ra ưu đãi phù hợp"
    }
  ],
  "ownedAt": 1750136401000,
  "userModelId": null,
  "keyLlmId": null
}
```

### 6. Error Handling

#### Possible Errors
1. **RESOURCE_NOT_FOUND**: Strategy gốc không tồn tại
2. **RESOURCE_SHARING_FAILED**: Lỗi khi lưu vào database
3. **Database constraint errors**: Duplicate entries, foreign key violations

#### Error Recovery
- Tất cả errors được log nhưng không throw để không làm fail transaction thanh toán
- User sẽ nhận được sản phẩm nhưng admin cần xử lý thủ công việc chia sẻ strategy

### 7. Benefits

1. **Automatic Sharing**: User tự động nhận được strategy sau khi mua
2. **Example Copy**: User có sẵn examples để tham khảo từ strategy gốc
3. **Customizable**: User có thể tùy chỉnh examples sau khi nhận
4. **Isolated**: Mỗi user có bản copy riêng, không ảnh hưởng lẫn nhau
5. **Extensible**: Có thể thêm userModelId và keyLlmId sau

### 8. Testing Scenarios

1. **Happy Path**: Mua strategy thành công, tạo AgentStrategyUser mới
2. **Duplicate Purchase**: User đã có strategy, không tạo duplicate
3. **Strategy Not Found**: Strategy gốc không tồn tại
4. **Strategy Deleted**: Strategy bị xóa (deletedBy != null)
5. **Database Error**: Lỗi khi save AgentStrategyUser

### 9. Future Enhancements

1. **Model Configuration**: Tự động set userModelId dựa trên user preferences
2. **API Key Integration**: Tự động link với keyLlmId của user
3. **Notification**: Thông báo cho user khi strategy được chia sẻ thành công
4. **Analytics**: Track usage của shared strategies
5. **Versioning**: Support multiple versions của strategy
