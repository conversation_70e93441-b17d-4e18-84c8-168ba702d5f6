# Agent Delete Error Analysis & Fix

## Tóm Tắt Lỗi

Khi thực hiện xóa agent, hệ thống gặp lỗi SQL nghiêm trọng dẫn đến transaction rollback và không thể hoàn thành việc xóa agent.

## Chi Tiết Lỗi

### 1. Lỗi Chính: Column không tồn tại
```sql
error: column page.userid does not exist
```

**Nguyên nhân**: 
- Trong method `removeFacebookPagesFromAgent()`, code sử dụng field `page.userId` không tồn tại
- Facebook Page entity không có field `userId` trực tiếp
- Facebook Page liên kết với User thông qua `facebook_personal_id` → `facebook_personal.user_id`

### 2. Lỗi Cascade: Transaction Rollback
```sql
error: current transaction is aborted, commands ignored until end of transaction block
```

**Nguyên nhân**:
- <PERSON>u lỗi đầu tiên, PostgreSQL abort transaction
- Tất cả queries tiếp theo bị reject cho đến khi transaction kết thúc
- Dẫn đến việc không thể xóa agent và các tài nguyên liên quan

### 3. Lỗi Field Mapping
- `page.userId` → Không tồn tại, cần JOIN với `facebook_personal`
- `website.deletedAt` → Không tồn tại trong `user_websites` entity
- `userAgentId` vs `user_agent_id` → Cần mapping đúng database column
- `agent-user-tools` vs `user_tools_agents` → Tên table entity không khớp với database

## Giải Pháp Đã Áp Dụng

### 1. Fix Facebook Pages Query
**Trước:**
```typescript
.andWhere('page.userId = :userId', { userId })
```

**Sau:**
```typescript
.innerJoin('facebook_personal', 'personal', 'page.facebook_personal_id = personal.id')
.andWhere('personal.user_id = :userId', { userId })
```

**Update Query Fix:**
```typescript
.andWhere(`facebook_personal_id IN (
  SELECT id FROM facebook_personal WHERE user_id = :userId
)`, { userId })
```

### 2. Fix User Websites Query
**Trước:**
```typescript
.andWhere('website.deletedAt IS NULL')
```

**Sau:**
```typescript
// Bỏ điều kiện deletedAt vì UserWebsite entity không có soft delete
```

**Field Mapping Fix:**
```typescript
// Sử dụng đúng database column names
.where('website.agent_id = :agentId', { agentId })
.andWhere('website.user_id = :userId', { userId })
```

### 3. Fix Agent User Tools Query
**Trước:**
```typescript
@Entity('agent-user-tools') // Table không tồn tại
```

**Sau:**
```typescript
@Entity('user_tools_agents') // Sử dụng đúng tên table trong database
.where('user_agent_id = :agentId', { agentId })
```

## Cấu Trúc Database Đã Xác Minh

### Facebook Page Entity
```typescript
@Entity('facebook_page')
export class FacebookPage {
  @Column({ name: 'facebook_personal_id', type: 'uuid' })
  facebookPersonalId: string;
  
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
```

### User Website Entity
```typescript
@Entity('user_websites')
export class UserWebsite {
  @Column({ name: 'user_id' })
  userId: number;
  
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string | null;
  
  // Không có deletedAt field
}
```

### Agent User Tools Entity
```typescript
@Entity('user_tools_agents')
export class AgentUserTools {
  @PrimaryColumn({ name: 'user_agent_id', type: 'uuid' })
  userAgentId: string;

  @PrimaryColumn({ name: 'custom_tool_id', type: 'uuid' })
  customToolId: string;
}
```

## Kết Quả

✅ **Fixed**: Facebook Pages removal với JOIN đúng relationship
✅ **Fixed**: User Websites removal với field mapping đúng
✅ **Fixed**: Agent User Tools removal với table name đúng
✅ **Fixed**: Entity table name mapping với database schema
✅ **Improved**: Error handling không làm gián đoạn transaction

## Recommendations

1. **Database Schema Review**: Cần review và chuẩn hóa soft delete pattern
2. **Entity Validation**: Đảm bảo entity fields match với database columns
3. **Integration Testing**: Thêm tests cho delete operations
4. **Transaction Management**: Cân nhắc sử dụng savepoints cho error recovery
