# Agent Type Validation Implementation Plan

## Tổng Quan

Thêm validation TypeAgent cho tất cả các controllers agent user để đảm bảo các tính năng chỉ được sử dụng khi TypeAgent config cho phép.

## Đã Hoàn Thà<PERSON>

### ✅ Phase 1: Infrastructure Setup
1. **AgentValidationService** - `src/modules/agent/user/services/agent-validation.service.ts`
   - `validateAgentAndFeature()` - Validate single feature
   - `validateAgentOwnership()` - Chỉ validate ownership
   - `validateAgentAndMultipleFeatures()` - Validate multiple features
   - `getFeatureDisplayName()` - Helper method

2. **Error Code** - `src/modules/agent/exceptions/agent-error.code.ts`
   - Thêm `AGENT_FEATURE_NOT_ENABLED: 40141`

3. **Feature Mapping** - `src/modules/agent/user/constants/agent-feature-mapping.ts`
   - Mapping các API endpoints với TypeAgent config fields
   - Helper functions: `getRequiredFeatures()`, `getFeatureDescription()`

4. **Module Integration**
   - Thêm AgentValidationService vào AgentUserModule
   - Export trong services/index.ts

### ✅ Phase 2: AgentFacebookPageService
- ✅ Updated `integrateFacebookPages()` method
- ✅ Updated `getFacebookPages()` method  
- ✅ Updated `removeFacebookPage()` method
- ✅ Removed old `checkAgentOwnership()` method
- ✅ Uses `FACEBOOK_PAGE` feature mapping

## Cần Thực Hiện

### 🔄 Phase 3: Update Remaining Services

#### 1. AgentWebsiteService
**File**: `src/modules/agent/user/services/agent-website.service.ts`
**Methods cần update**:
- `integrateWebsites()` - Validate `WEBSITE` feature
- `getWebsites()` - Validate `WEBSITE` feature
- `removeWebsite()` - Validate `WEBSITE` feature

#### 2. AgentZaloService  
**File**: `src/modules/agent/user/services/agent-zalo.service.ts`
**Methods cần update**:
- `integrateZaloOfficialAccounts()` - Validate `ZALO_OA` feature
- `getZaloOfficialAccounts()` - Validate `ZALO_OA` feature
- `removeZaloOfficialAccount()` - Validate `ZALO_OA` feature

#### 3. AgentResourceUserService
**File**: `src/modules/agent/user/services/agent-resource-user.service.ts`
**Methods cần update**:
- `getAgentUrls()` - Validate `RESOURCES` feature
- `addUrlsToAgent()` - Validate `RESOURCES` feature
- `removeUrlsFromAgent()` - Validate `RESOURCES` feature
- `getAgentMedias()` - Validate `RESOURCES` feature
- `addMediasToAgent()` - Validate `RESOURCES` feature
- `removeMediasFromAgent()` - Validate `RESOURCES` feature
- `getAgentProducts()` - Validate `RESOURCES` feature
- `addProductsToAgent()` - Validate `RESOURCES` feature
- `removeProductsFromAgent()` - Validate `RESOURCES` feature

#### 4. AgentStrategyService
**File**: `src/modules/agent/user/services/agent-strategy.service.ts`
**Methods cần update**:
- `getAgentStrategyUsers()` - Validate `STRATEGY` feature
- `updateAgentStrategyUser()` - Validate `STRATEGY` feature
- `setDefaultAgentStrategyUser()` - Validate `STRATEGY` feature

#### 5. ProfileUserService
**File**: `src/modules/agent/user/services/profile-user.service.ts`
**Methods cần update**:
- Tất cả methods - Validate `PROFILE` feature

#### 6. ConversionUserService
**File**: `src/modules/agent/user/services/conversion-user.service.ts`
**Methods cần update**:
- Tất cả methods - Validate `CONVERSION` feature

#### 7. MultiAgentUserService
**File**: `src/modules/agent/user/services/multi-agent-user.service.ts`
**Methods cần update**:
- Tất cả methods - Validate `MULTI_AGENT` feature

## Implementation Pattern

### Standard Pattern cho mỗi method:

```typescript
// Thay thế
await this.checkAgentOwnership(agentId, userId);

// Bằng
await this.agentValidationService.validateAgentAndMultipleFeatures(
  agentId,
  userId,
  getRequiredFeatures('FEATURE_KEY')
);
```

### Import Requirements:

```typescript
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
```

### Constructor Injection:

```typescript
constructor(
  // ... existing dependencies
  private readonly agentValidationService: AgentValidationService,
) {}
```

## Feature Mapping Reference

| Controller | Feature Key | TypeAgent Config Field |
|------------|-------------|------------------------|
| AgentFacebookPageController | `FACEBOOK_PAGE` | `enableOutputToMessenger` |
| AgentWebsiteController | `WEBSITE` | `enableOutputToWebsiteLiveChat` |
| AgentZaloController | `ZALO_OA` | `enableOutputToZaloOA` |
| AgentResourceUserController | `RESOURCES` | `enableResourceUsage` |
| AgentStrategyController | `STRATEGY` | `enableDynamicStrategyExecution` |
| ProfileUserController | `PROFILE` | `enableAgentProfileCustomization` |
| ConversionUserController | `CONVERSION` | `enableTaskConversionTracking` |
| MultiAgentUserController | `MULTI_AGENT` | `enableMultiAgentCollaboration` |

## Testing Plan

### Unit Tests
- Test AgentValidationService methods
- Test feature mapping functions
- Test error scenarios

### Integration Tests  
- Test each controller với TypeAgent config enabled/disabled
- Test error responses khi feature không enabled

## Documentation Updates

### API Documentation
- Update error responses trong controllers
- Thêm `AGENT_FEATURE_NOT_ENABLED` vào @ApiErrorResponse decorators

### README Updates
- Document new validation logic
- Update troubleshooting guide

## Rollout Strategy

1. **Phase 3a**: Update AgentWebsiteService và AgentZaloService
2. **Phase 3b**: Update AgentResourceUserService  
3. **Phase 3c**: Update remaining services (Strategy, Profile, Conversion, MultiAgent)
4. **Phase 4**: Testing và documentation
5. **Phase 5**: Deployment và monitoring

## Success Criteria

- ✅ Tất cả controllers validate TypeAgent features
- ✅ Proper error messages khi feature không enabled
- ✅ Backward compatibility maintained
- ✅ Performance impact minimal
- ✅ Code coverage maintained
