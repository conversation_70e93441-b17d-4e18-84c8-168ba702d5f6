import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { QueryDto } from '@common/dto';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentRepository } from '@modules/agent/repositories';
import { ZaloOfficialAccountRepository } from '@modules/marketing/user/repositories';
import { In } from 'typeorm';
import {
  AddZaloOfficialAccountsDto,
  AgentZaloOfficialAccountResponseDto,
  RemoveZaloOfficialAccountsDto,
  ZaloOfficialAccountOperationResultDto,
} from '../dto/zalo/agent-zalo.dto';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';

/**
 * Service xử lý các thao tác liên quan đến <PERSON>alo Official Accounts trong agent
 */
@Injectable()
export class AgentZaloService {
  private readonly logger = new Logger(AgentZaloService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly zaloOfficialAccountRepository: ZaloOfficialAccountRepository,
  ) {}

  /**
   * Thêm nhiều Zalo Official Accounts vào agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param addDto DTO chứa danh sách OA IDs
   * @returns Kết quả thao tác
   */
  async addZaloOfficialAccounts(
    userId: number,
    agentId: string,
    addDto: AddZaloOfficialAccountsDto,
  ): Promise<ZaloOfficialAccountOperationResultDto> {
    try {
      this.logger.log(`Thêm Zalo OAs vào agent ${agentId} cho user ${userId}`);

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentRepository.existsByIdAndUserId(
        agentId,
        userId,
      );
      if (!agentExists) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Không tìm thấy agent hoặc agent không thuộc về bạn',
        );
      }

      // Kiểm tra danh sách OA IDs có hợp lệ không
      if (
        !addDto.zaloOfficialAccountIds ||
        addDto.zaloOfficialAccountIds.length === 0
      ) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Danh sách Zalo Official Account không được để trống',
        );
      }

      // Lấy tất cả Zalo OAs trong một truy vấn duy nhất để validate
      const zaloOAs = await this.zaloOfficialAccountRepository.find({
        where: {
          id: In(addDto.zaloOfficialAccountIds),
          userId: userId,
          status: 'active', // Chỉ cho phép kết nối với OA đang hoạt động
        },
      });

      if (zaloOAs.length === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Không tìm thấy Zalo Official Account hợp lệ nào thuộc về bạn',
        );
      }

      // Tạo map để tra cứu nhanh
      const zaloOAMap = new Map(zaloOAs.map((oa) => [oa.id, oa]));

      // Kiểm tra và validate từng OA
      for (const oaId of addDto.zaloOfficialAccountIds) {
        const zaloOA = zaloOAMap.get(oaId);

        if (!zaloOA) {
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
            `Zalo Official Account ${oaId} không tồn tại hoặc không thuộc về bạn`,
          );
        }

        // Kiểm tra OA đã được gán cho agent khác chưa
        if (zaloOA.agentId && zaloOA.agentId !== agentId) {
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
            `Zalo Official Account ${oaId} đã được kết nối với agent khác`,
          );
        }
      }

      // Thực hiện bulk add sau khi đã validate tất cả
      const result = await this.zaloOfficialAccountRepository.bulkAddToAgent(
        agentId,
        userId,
        addDto.zaloOfficialAccountIds,
      );

      this.logger.log(
        `Hoàn thành thêm Zalo OAs: ${result.successCount} thành công, ${result.skippedCount} bỏ qua`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Zalo OAs: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể thêm Zalo Official Accounts vào agent',
      );
    }
  }

  /**
   * Gỡ nhiều Zalo Official Accounts khỏi agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param removeDto DTO chứa danh sách OA IDs
   * @returns Kết quả thao tác
   */
  async removeZaloOfficialAccounts(
    userId: number,
    agentId: string,
    removeDto: RemoveZaloOfficialAccountsDto,
  ): Promise<ZaloOfficialAccountOperationResultDto> {
    try {
      this.logger.log(`Gỡ Zalo OAs khỏi agent ${agentId} cho user ${userId}`);

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentRepository.existsByIdAndUserId(
        agentId,
        userId,
      );
      if (!agentExists) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Không tìm thấy agent hoặc agent không thuộc về bạn',
        );
      }

      // Kiểm tra danh sách OA IDs có hợp lệ không
      if (
        !removeDto.zaloOfficialAccountIds ||
        removeDto.zaloOfficialAccountIds.length === 0
      ) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Danh sách Zalo Official Account không được để trống',
        );
      }

      // Lấy tất cả Zalo OAs đang được gán cho agent này để validate
      const zaloOAs = await this.zaloOfficialAccountRepository.find({
        where: {
          id: In(removeDto.zaloOfficialAccountIds),
          userId: userId,
          agentId: agentId, // Chỉ lấy những OA đang được gán cho agent này
        },
      });

      if (zaloOAs.length === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Không tìm thấy Zalo Official Account nào đang được gán cho agent này',
        );
      }

      // Tạo map để tra cứu nhanh
      const zaloOAMap = new Map(zaloOAs.map((oa) => [oa.id, oa]));

      // Kiểm tra và validate từng OA
      for (const oaId of removeDto.zaloOfficialAccountIds) {
        const zaloOA = zaloOAMap.get(oaId);

        if (!zaloOA) {
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
            `Zalo Official Account ${oaId} không được gán cho agent này hoặc không thuộc về bạn`,
          );
        }
      }

      // Thực hiện bulk remove sau khi đã validate tất cả
      const result =
        await this.zaloOfficialAccountRepository.bulkRemoveFromAgent(
          agentId,
          userId,
          removeDto.zaloOfficialAccountIds,
        );

      this.logger.log(
        `Hoàn thành gỡ Zalo OAs: ${result.successCount} thành công, ${result.skippedCount} bỏ qua`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ Zalo OAs: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể gỡ Zalo Official Accounts khỏi agent',
      );
    }
  }

  /**
   * Lấy danh sách Zalo Official Accounts được gán trong agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách OAs có phân trang
   */
  async getAgentZaloOfficialAccounts(
    userId: number,
    agentId: string,
    queryDto: QueryDto,
  ): Promise<PaginatedResult<AgentZaloOfficialAccountResponseDto>> {
    try {
      this.logger.log(
        `Lấy danh sách Zalo OAs của agent ${agentId} cho user ${userId}`,
      );

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentRepository.existsByIdAndUserId(
        agentId,
        userId,
      );
      if (!agentExists) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Không tìm thấy agent hoặc agent không thuộc về bạn',
        );
      }

      // Lấy danh sách với phân trang
      const { items, total } =
        await this.zaloOfficialAccountRepository.findByAgentIdPaginated(
          agentId,
          userId,
          queryDto.page,
          queryDto.limit,
          queryDto.search,
          undefined, // status - không có trong QueryDto
        );

      // Map sang response DTO
      const responseItems: AgentZaloOfficialAccountResponseDto[] = items.map(
        (oa) => ({
          id: oa.id,
          oaId: oa.oaId,
          name: oa.name,
          description: oa.description,
          avatarUrl: oa.avatarUrl,
          status: oa.status,
          createdAt: oa.createdAt,
          updatedAt: oa.updatedAt,
        }),
      );

      const totalPages = Math.ceil(total / queryDto.limit!);

      this.logger.log(`Tìm thấy ${total} Zalo OAs cho agent ${agentId}`);

      return {
        items: responseItems,
        meta: {
          totalItems: total,
          itemCount: responseItems.length,
          itemsPerPage: queryDto.limit!,
          totalPages,
          currentPage: queryDto.page!,
          hasItems: total > 0,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Zalo OAs: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể lấy danh sách Zalo Official Accounts của agent',
      );
    }
  }

  /**
   * Gỡ tất cả Zalo Official Accounts khỏi agent (dùng khi xóa agent)
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @returns Số lượng đã gỡ
   */
  async removeAllZaloOfficialAccounts(
    userId: number,
    agentId: string,
  ): Promise<number> {
    try {
      this.logger.log(
        `Gỡ tất cả Zalo OAs khỏi agent ${agentId} cho user ${userId}`,
      );

      const removedCount =
        await this.zaloOfficialAccountRepository.removeAllFromAgent(
          agentId,
          userId,
        );

      this.logger.log(`Đã gỡ ${removedCount} Zalo OAs khỏi agent ${agentId}`);
      return removedCount;
    } catch (error) {
      this.logger.error(
        `Lỗi khi gỡ tất cả Zalo OAs: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể gỡ tất cả Zalo Official Accounts khỏi agent',
      );
    }
  }
}
