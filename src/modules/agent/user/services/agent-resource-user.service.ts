import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentUserRepository,
  AgentUrlRepository,
  AgentMediaRepository,
  AgentProductRepository
} from '@modules/agent/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { UserProductRepository } from '@modules/business/repositories';
import { CdnService } from '@shared/services/cdn.service';
import { In } from 'typeorm';
import {
  AddAgentUrlDto,
  AddAgentMediaDto,
  AddAgentProductDto,
  RemoveAgentUrlDto,
  RemoveAgentMediaDto,
  RemoveAgentProductDto,
  AgentUrlQueryDto,
  AgentMediaQueryDto,
  AgentProductQueryDto,
  AgentUrlResponseDto,
  AgentMediaResponseDto,
  AgentProductResponseDto,
  BulkUrlOperationResponseDto,
  BulkMediaOperationResponseDto,
  BulkProductOperationResponseDto
} from '../dto/resource';
import { AgentResourceMapper } from '../mappers';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';

/**
 * Service xử lý các thao tác liên quan đến tài nguyên của agent cho người dùng
 */
@Injectable()
export class AgentResourceUserService {
  private readonly logger = new Logger(AgentResourceUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly urlRepository: UrlRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly cdnService: CdnService,
    private readonly agentValidationService: AgentValidationService,
  ) { }

  // ==================== URL METHODS ====================

  /**
   * Lấy danh sách URL của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách URL có phân trang
   */
  async getAgentUrls(
    agentId: string,
    userId: number,
    queryDto: AgentUrlQueryDto,
  ): Promise<PaginatedResult<AgentUrlResponseDto>> {
    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Lấy danh sách URL IDs từ agent_url
      const agentUrls = await this.agentUrlRepository.findByAgentId(agentId);
      const urlIds = agentUrls.map(au => au.urlId);

      if (urlIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết URL từ url_data với phân trang
      const urls = await this.urlRepository.findUrlsByIds(urlIds.slice(
        (queryDto.page - 1) * queryDto.limit,
        queryDto.page * queryDto.limit
      ));

      const total = urlIds.length;
      const items = AgentResourceMapper.toUrlResponseDtos(urls);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách URL của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm URL vào agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin URL cần thêm
   * @returns Kết quả bulk operation
   */
  async addAgentUrls(
    agentId: string,
    userId: number,
    addDto: AddAgentUrlDto,
  ): Promise<BulkUrlOperationResponseDto> {
    const result: BulkUrlOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: addDto.urlIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Bulk validate URL ownership
      const validationResult = await this.bulkValidateUrlOwnership(addDto.urlIds, userId);

      // Thêm các URL không hợp lệ vào failed list
      for (const [urlId, error] of Object.entries(validationResult.errors)) {
        result.idFailed.push(urlId);
        result.errors[urlId] = error;
      }

      // Lấy danh sách URL hợp lệ
      const validUrlIds = validationResult.validIds;

      if (validUrlIds.length > 0) {
        // Kiểm tra URL nào đã tồn tại trong agent
        const existingAgentUrls = await this.agentUrlRepository.find({
          where: { agentId, urlId: In(validUrlIds) },
        });
        const existingUrlIds = new Set(existingAgentUrls.map(au => au.urlId));

        // Bulk insert các URL chưa tồn tại
        const urlsToInsert = validUrlIds.filter(urlId => !existingUrlIds.has(urlId));

        if (urlsToInsert.length > 0) {
          const agentUrls = urlsToInsert.map(urlId =>
            this.agentUrlRepository.create({ agentId, urlId })
          );
          await this.agentUrlRepository.save(agentUrls);
        }

        // Tất cả URL hợp lệ đều được coi là thành công (kể cả đã tồn tại)
        result.idSuccess = validUrlIds;
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk add URLs to agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm URL vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Gỡ bỏ URL khỏi agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin URL cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  async removeAgentUrls(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentUrlDto,
  ): Promise<BulkUrlOperationResponseDto> {
    const result: BulkUrlOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: removeDto.urlIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Kiểm tra URL nào thực sự tồn tại trong agent
      const existingAgentUrls = await this.agentUrlRepository.find({
        where: { agentId, urlId: In(removeDto.urlIds) },
      });
      const existingUrlIds = new Set(existingAgentUrls.map(au => au.urlId));

      // Phân loại URL thành tồn tại và không tồn tại
      for (const urlId of removeDto.urlIds) {
        if (existingUrlIds.has(urlId)) {
          result.idSuccess.push(urlId);
        } else {
          result.idFailed.push(urlId);
          result.errors[urlId] = 'URL không tồn tại trong agent';
        }
      }

      // Bulk delete các URL tồn tại
      if (result.idSuccess.length > 0) {
        await this.agentUrlRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('urlId IN (:...urlIds)', { urlIds: result.idSuccess })
          .execute();
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk remove URLs from agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ URL khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== MEDIA METHODS ====================

  /**
   * Lấy danh sách Media của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Media có phân trang
   */
  async getAgentMedias(
    agentId: string,
    userId: number,
    queryDto: AgentMediaQueryDto,
  ): Promise<PaginatedResult<AgentMediaResponseDto>> {
    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Lấy danh sách Media IDs từ agents_media
      const agentMedias = await this.agentMediaRepository.findByAgentId(agentId);
      const mediaIds = agentMedias.map(am => am.mediaId);

      if (mediaIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết Media từ media_data với phân trang
      const medias = await this.mediaRepository.findByIds(mediaIds.slice(
        (queryDto.page - 1) * queryDto.limit,
        queryDto.page * queryDto.limit
      ));

      const total = mediaIds.length;
      const items = AgentResourceMapper.toMediaResponseDtos(medias, this.cdnService);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Media của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm Media vào agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin Media cần thêm
   * @returns Kết quả bulk operation
   */
  async addAgentMedias(
    agentId: string,
    userId: number,
    addDto: AddAgentMediaDto,
  ): Promise<BulkMediaOperationResponseDto> {
    const result: BulkMediaOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: addDto.mediaIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Bulk validate Media ownership
      const validationResult = await this.bulkValidateMediaOwnership(addDto.mediaIds, userId);

      // Thêm các Media không hợp lệ vào failed list
      for (const [mediaId, error] of Object.entries(validationResult.errors)) {
        result.idFailed.push(mediaId);
        result.errors[mediaId] = error;
      }

      // Lấy danh sách Media hợp lệ
      const validMediaIds = validationResult.validIds;

      if (validMediaIds.length > 0) {
        // Kiểm tra Media nào đã tồn tại trong agent
        const existingAgentMedias = await this.agentMediaRepository.find({
          where: { agentId, mediaId: In(validMediaIds) },
        });
        const existingMediaIds = new Set(existingAgentMedias.map(am => am.mediaId));

        // Bulk insert các Media chưa tồn tại
        const mediasToInsert = validMediaIds.filter(mediaId => !existingMediaIds.has(mediaId));

        if (mediasToInsert.length > 0) {
          const agentMedias = mediasToInsert.map(mediaId =>
            this.agentMediaRepository.create({ agentId, mediaId })
          );
          await this.agentMediaRepository.save(agentMedias);
        }

        // Tất cả Media hợp lệ đều được coi là thành công (kể cả đã tồn tại)
        result.idSuccess = validMediaIds;
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk add Medias to agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Media vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Gỡ bỏ Media khỏi agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin Media cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  async removeAgentMedias(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentMediaDto,
  ): Promise<BulkMediaOperationResponseDto> {
    const result: BulkMediaOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: removeDto.mediaIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Kiểm tra Media nào thực sự tồn tại trong agent
      const existingAgentMedias = await this.agentMediaRepository.find({
        where: { agentId, mediaId: In(removeDto.mediaIds) },
      });
      const existingMediaIds = new Set(existingAgentMedias.map(am => am.mediaId));

      // Phân loại Media thành tồn tại và không tồn tại
      for (const mediaId of removeDto.mediaIds) {
        if (existingMediaIds.has(mediaId)) {
          result.idSuccess.push(mediaId);
        } else {
          result.idFailed.push(mediaId);
          result.errors[mediaId] = 'Media không tồn tại trong agent';
        }
      }

      // Bulk delete các Media tồn tại
      if (result.idSuccess.length > 0) {
        await this.agentMediaRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('mediaId IN (:...mediaIds)', { mediaIds: result.idSuccess })
          .execute();
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk remove Medias from agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Media khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== PRODUCT METHODS ====================

  /**
   * Lấy danh sách Product của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Product có phân trang
   */
  async getAgentProducts(
    agentId: string,
    userId: number,
    queryDto: AgentProductQueryDto,
  ): Promise<PaginatedResult<AgentProductResponseDto>> {
    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Lấy danh sách Product IDs từ agents_product
      const agentProducts = await this.agentProductRepository.findByAgentId(agentId);
      const productIds = agentProducts.map(ap => ap.productId);

      if (productIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết Product từ user_products với phân trang
      const paginatedProductIds = productIds.slice(
        (queryDto.page - 1) * queryDto.limit,
        queryDto.page * queryDto.limit
      );

      const products = await this.userProductRepository.createQueryBuilder('product')
        .where('product.id IN (:...productIds)', { productIds: paginatedProductIds })
        .orderBy('product.createdAt', 'DESC')
        .getMany();

      const total = productIds.length;
      const items = AgentResourceMapper.toProductResponseDtos(products);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Product của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm Product vào agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin Product cần thêm
   * @returns Kết quả bulk operation
   */
  async addAgentProducts(
    agentId: string,
    userId: number,
    addDto: AddAgentProductDto,
  ): Promise<BulkProductOperationResponseDto> {
    const result: BulkProductOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: addDto.productIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Bulk validate Product ownership
      const validationResult = await this.bulkValidateProductOwnership(addDto.productIds, userId);

      // Thêm các Product không hợp lệ vào failed list
      for (const [productIdStr, error] of Object.entries(validationResult.errors)) {
        const productId = parseInt(productIdStr);
        result.idFailed.push(productId);
        result.errors[productIdStr] = error;
      }

      // Lấy danh sách Product hợp lệ
      const validProductIds = validationResult.validIds;

      if (validProductIds.length > 0) {
        // Kiểm tra Product nào đã tồn tại trong agent
        const existingAgentProducts = await this.agentProductRepository.find({
          where: { agentId, productId: In(validProductIds) },
        });
        const existingProductIds = new Set(existingAgentProducts.map(ap => ap.productId));

        // Bulk insert các Product chưa tồn tại
        const productsToInsert = validProductIds.filter(productId => !existingProductIds.has(productId));

        if (productsToInsert.length > 0) {
          const agentProducts = productsToInsert.map(productId =>
            this.agentProductRepository.create({ agentId, productId })
          );
          await this.agentProductRepository.save(agentProducts);
        }

        // Tất cả Product hợp lệ đều được coi là thành công (kể cả đã tồn tại)
        result.idSuccess = validProductIds;
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk add Products to agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Product vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Gỡ bỏ Product khỏi agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin Product cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  async removeAgentProducts(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentProductDto,
  ): Promise<BulkProductOperationResponseDto> {
    const result: BulkProductOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: removeDto.productIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Resources feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES')
      );

      // Debug log để kiểm tra input
      this.logger.debug(`Removing products from agent ${agentId}: ${JSON.stringify(removeDto.productIds)}`);

      // Kiểm tra Product nào thực sự tồn tại trong agent
      // Sử dụng query builder để đảm bảo kiểu dữ liệu chính xác
      const existingAgentProducts = await this.agentProductRepository
        .createQueryBuilder('agentProduct')
        .where('agentProduct.agentId = :agentId', { agentId })
        .andWhere('agentProduct.productId IN (:...productIds)', { productIds: removeDto.productIds })
        .getMany();

      // Debug log để kiểm tra kết quả query
      this.logger.debug(`Found existing agent products: ${JSON.stringify(existingAgentProducts.map(ap => ap.productId))}`);

      // Tạo Set với kiểu dữ liệu number để đảm bảo so sánh chính xác
      const existingProductIds = new Set<number>(existingAgentProducts.map(ap => Number(ap.productId)));

      // Phân loại Product thành tồn tại và không tồn tại
      for (const productId of removeDto.productIds) {
        const numericProductId = Number(productId);
        if (existingProductIds.has(numericProductId)) {
          result.idSuccess.push(productId);
        } else {
          result.idFailed.push(productId);
          result.errors[productId.toString()] = 'Product không tồn tại trong agent';
        }
      }

      // Debug log để kiểm tra phân loại
      this.logger.debug(`Success IDs: ${JSON.stringify(result.idSuccess)}`);
      this.logger.debug(`Failed IDs: ${JSON.stringify(result.idFailed)}`);

      // Bulk delete các Product tồn tại
      if (result.idSuccess.length > 0) {
        const deleteResult = await this.agentProductRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('productId IN (:...productIds)', { productIds: result.idSuccess })
          .execute();

        this.logger.debug(`Delete result affected: ${deleteResult.affected}`);
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk remove Products from agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Product khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== VALIDATION METHODS ====================





  /**
   * Bulk validate URL ownership với partial success
   * @param urlIds Danh sách ID của URL
   * @param userId ID của người dùng
   * @returns Kết quả validation với danh sách valid và invalid IDs
   */
  private async bulkValidateUrlOwnership(
    urlIds: string[],
    userId: number
  ): Promise<{ validIds: string[]; errors: Record<string, string> }> {
    const result = {
      validIds: [] as string[],
      errors: {} as Record<string, string>,
    };

    // Bulk query để lấy tất cả URLs cùng lúc
    const urls = await this.urlRepository.findUrlsByIds(urlIds);
    const urlMap = new Map(urls.map(url => [url.id, url]));

    for (const urlId of urlIds) {
      const url = urlMap.get(urlId);

      if (!url) {
        result.errors[urlId] = 'URL không tồn tại';
      } else if (url.ownedBy !== userId) {
        result.errors[urlId] = 'URL không thuộc về user';
      } else {
        result.validIds.push(urlId);
      }
    }

    return result;
  }

  /**
   * Bulk validate Media ownership với partial success
   * @param mediaIds Danh sách ID của Media
   * @param userId ID của người dùng
   * @returns Kết quả validation với danh sách valid và invalid IDs
   */
  private async bulkValidateMediaOwnership(
    mediaIds: string[],
    userId: number
  ): Promise<{ validIds: string[]; errors: Record<string, string> }> {
    const result = {
      validIds: [] as string[],
      errors: {} as Record<string, string>,
    };

    // Bulk query để lấy tất cả Medias cùng lúc
    const medias = await this.mediaRepository.findByIds(mediaIds);
    const mediaMap = new Map(medias.map(media => [media.id, media]));

    for (const mediaId of mediaIds) {
      const media = mediaMap.get(mediaId);

      if (!media) {
        result.errors[mediaId] = 'Media không tồn tại';
      } else if (media.ownedBy !== userId) {
        result.errors[mediaId] = 'Media không thuộc về user';
      } else {
        result.validIds.push(mediaId);
      }
    }

    return result;
  }

  /**
   * Bulk validate Product ownership với partial success
   * @param productIds Danh sách ID của Product
   * @param userId ID của người dùng
   * @returns Kết quả validation với danh sách valid và invalid IDs
   */
  private async bulkValidateProductOwnership(
    productIds: number[],
    userId: number
  ): Promise<{ validIds: number[]; errors: Record<string, string> }> {
    const result = {
      validIds: [] as number[],
      errors: {} as Record<string, string>,
    };

    // Sử dụng method mới từ repository để chỉ lấy created_by
    const productOwnershipData = await this.userProductRepository.findCreatedByForProducts(productIds);
    const productMap = new Map(productOwnershipData.map(item => [item.id, item.createdBy]));

    for (const productId of productIds) {
      const createdBy = productMap.get(productId);

      if (createdBy === undefined) {
        result.errors[productId.toString()] = 'Product không tồn tại';
      } else if (createdBy !== userId) {
        result.errors[productId.toString()] = 'Product không thuộc về user';
      } else {
        result.validIds.push(productId);
      }
    }

    return result;
  }
}