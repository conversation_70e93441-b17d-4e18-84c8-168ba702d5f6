import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { 
  AgentUserRepository,
  UserMultiAgentRepository,
  AgentRepository
} from '@modules/agent/repositories';
import { CdnService } from '@shared/services/cdn.service';
import { In, IsNull } from 'typeorm';
import { 
  AddMultiAgentDto,
  RemoveMultiAgentDto,
  MultiAgentQueryDto,
  MultiAgentResponseDto,
  BulkMultiAgentOperationResponseDto
} from '../dto/multi-agent';
import { MultiAgentMapper } from '../mappers';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';

/**
 * Service xử lý các thao tác liên quan đến multi-agent system cho người dùng
 */
@Injectable()
export class MultiAgentUserService {
  private readonly logger = new Logger(MultiAgentUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly userMultiAgentRepository: UserMultiAgentRepository,
    private readonly agentRepository: AgentRepository,
    private readonly cdnService: CdnService,
    private readonly agentValidationService: AgentValidationService,
  ) { }

  /**
   * Lấy danh sách agent con của agent cha
   * @param parentAgentId ID của agent cha
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent con có phân trang
   */
  async getMultiAgents(
    parentAgentId: string,
    userId: number,
    queryDto: MultiAgentQueryDto,
  ): Promise<PaginatedResult<MultiAgentResponseDto>> {
    try {
      // Validate agent ownership và Multi-Agent feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        parentAgentId,
        userId,
        getRequiredFeatures('MULTI_AGENT')
      );

      // Lấy danh sách quan hệ multi-agent
      const relations = await this.userMultiAgentRepository.findChildrenByParentId(parentAgentId);

      if (relations.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy danh sách ID agent con
      const childAgentIds = relations.map(r => r.childAgentId);

      // Lấy thông tin chi tiết các agent con
      const childAgents = await this.agentRepository.find({
        where: {
          id: In(childAgentIds),
          deletedAt: IsNull()
        },
      });

      // Tạo Map để lookup nhanh
      const childAgentMap = new Map(childAgents.map(agent => [agent.id, agent]));

      // Filter theo search nếu có
      let filteredRelations = relations;
      if (queryDto.search || queryDto.promptSearch) {
        filteredRelations = relations.filter(relation => {
          const agent = childAgentMap.get(relation.childAgentId);
          if (!agent) return false;

          let matchSearch = true;
          let matchPromptSearch = true;

          // Tìm kiếm chung (search) - tìm trong cả tên agent và prompt
          if (queryDto.search) {
            const searchTerm = queryDto.search.toLowerCase();
            const nameMatch = agent.name.toLowerCase().includes(searchTerm);
            const promptMatch = relation.prompt?.toLowerCase().includes(searchTerm) || false;
            matchSearch = nameMatch || promptMatch;
          }

          // Tìm kiếm riêng theo prompt
          if (queryDto.promptSearch) {
            matchPromptSearch = relation.prompt?.toLowerCase().includes(queryDto.promptSearch.toLowerCase()) || false;
          }

          return matchSearch && matchPromptSearch;
        });
      }

      // Phân trang
      const total = filteredRelations.length;
      const startIndex = (queryDto.page - 1) * queryDto.limit;
      const endIndex = startIndex + queryDto.limit;
      const paginatedRelations = filteredRelations.slice(startIndex, endIndex);

      // Chuyển đổi sang DTO
      const items = MultiAgentMapper.toMultiAgentResponseDtos(
        paginatedRelations, 
        childAgentMap, 
        this.cdnService
      );

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách multi-agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm agent con vào multi-agent system với bulk operation
   * @param parentAgentId ID của agent cha
   * @param userId ID của người dùng
   * @param addDto Thông tin agent con cần thêm
   * @returns Kết quả bulk operation
   */
  async addMultiAgents(
    parentAgentId: string,
    userId: number,
    addDto: AddMultiAgentDto,
  ): Promise<BulkMultiAgentOperationResponseDto> {
    const result: BulkMultiAgentOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: addDto.multiAgents.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Multi-Agent feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        parentAgentId,
        userId,
        getRequiredFeatures('MULTI_AGENT')
      );

      // Kiểm tra không được tự tham chiếu
      const selfReferencingIds = addDto.multiAgents.filter(item => item.agent_id === parentAgentId);
      if (selfReferencingIds.length > 0) {
        for (const item of selfReferencingIds) {
          result.idFailed.push(item.agent_id);
          result.errors[item.agent_id] = AGENT_ERROR_CODES.MULTI_AGENT_SELF_REFERENCE.message;
        }
      }

      // Lấy danh sách agent IDs hợp lệ (không tự tham chiếu)
      const validAgentItems = addDto.multiAgents.filter(item => item.agent_id !== parentAgentId);
      const childAgentIds = validAgentItems.map(item => item.agent_id);

      // Bulk validate agent ownership
      const validationResult = await this.bulkValidateChildAgentOwnership(childAgentIds, userId);

      // Thêm các agent không hợp lệ vào failed list
      for (const [agentId, error] of Object.entries(validationResult.errors)) {
        result.idFailed.push(agentId);
        result.errors[agentId] = error;
      }

      // Lấy danh sách agent hợp lệ
      const validChildAgentIds = validationResult.validIds;

      if (validChildAgentIds.length > 0) {
        // Kiểm tra quan hệ nào đã tồn tại
        const existingRelations = await this.userMultiAgentRepository.find({
          where: {
            parentAgentId,
            childAgentId: In(validChildAgentIds)
          },
        });
        const existingChildIds = new Set(existingRelations.map(r => r.childAgentId));

        // Bulk insert các quan hệ chưa tồn tại
        const relationsToInsert = validAgentItems.filter(item =>
          validChildAgentIds.includes(item.agent_id) && !existingChildIds.has(item.agent_id)
        );

        if (relationsToInsert.length > 0) {
          const multiAgentRelations = relationsToInsert.map(item =>
            this.userMultiAgentRepository.create({
              parentAgentId,
              childAgentId: item.agent_id,
              prompt: item.prompt,
            })
          );
          await this.userMultiAgentRepository.save(multiAgentRelations);
        }

        // Tất cả agent hợp lệ đều được coi là thành công (kể cả đã tồn tại)
        result.idSuccess = validChildAgentIds;
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk add multi-agents to parent ${parentAgentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm multi-agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Gỡ bỏ agent con khỏi multi-agent system với bulk operation
   * @param parentAgentId ID của agent cha
   * @param userId ID của người dùng
   * @param removeDto Thông tin agent con cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  async removeMultiAgents(
    parentAgentId: string,
    userId: number,
    removeDto: RemoveMultiAgentDto,
  ): Promise<BulkMultiAgentOperationResponseDto> {
    const result: BulkMultiAgentOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: removeDto.childAgentIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Multi-Agent feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        parentAgentId,
        userId,
        getRequiredFeatures('MULTI_AGENT')
      );

      // Kiểm tra quan hệ nào thực sự tồn tại
      const existingRelations = await this.userMultiAgentRepository.find({
        where: {
          parentAgentId,
          childAgentId: In(removeDto.childAgentIds)
        },
      });
      const existingChildIds = new Set(existingRelations.map(r => r.childAgentId));

      // Phân loại agent thành tồn tại và không tồn tại
      for (const childAgentId of removeDto.childAgentIds) {
        if (existingChildIds.has(childAgentId)) {
          result.idSuccess.push(childAgentId);
        } else {
          result.idFailed.push(childAgentId);
          result.errors[childAgentId] = 'Quan hệ multi-agent không tồn tại';
        }
      }

      // Bulk delete các quan hệ tồn tại
      if (result.idSuccess.length > 0) {
        await this.userMultiAgentRepository.createQueryBuilder()
          .delete()
          .where('parentAgentId = :parentAgentId', { parentAgentId })
          .andWhere('childAgentId IN (:...childAgentIds)', { childAgentIds: result.idSuccess })
          .execute();
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk remove multi-agents from parent ${parentAgentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ multi-agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== VALIDATION METHODS ====================



  /**
   * Bulk validate child agent ownership với partial success
   * @param childAgentIds Danh sách ID của child agents
   * @param userId ID của người dùng
   * @returns Kết quả validation với danh sách valid và invalid IDs
   */
  private async bulkValidateChildAgentOwnership(
    childAgentIds: string[],
    userId: number
  ): Promise<{ validIds: string[]; errors: Record<string, string> }> {
    const result = {
      validIds: [] as string[],
      errors: {} as Record<string, string>,
    };

    // Bulk query để kiểm tra tất cả agents cùng lúc
    const agentUsers = await this.agentUserRepository.find({
      where: {
        id: In(childAgentIds),
        userId
      },
    });
    const validAgentIds = new Set(agentUsers.map(au => au.id));

    for (const agentId of childAgentIds) {
      if (validAgentIds.has(agentId)) {
        result.validIds.push(agentId);
      } else {
        result.errors[agentId] = 'Agent không tồn tại hoặc không thuộc về user';
      }
    }

    return result;
  }
}
