import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import { AgentUserRepository, AgentStrategyRepository } from '@modules/agent/repositories';
import { AgentStrategyUser } from '@modules/agent/entities/agents-strategy-user.entity';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error-codes';
import { STRATEGY_ERROR_CODES } from '@modules/agent/exceptions/strategy-error.code';
import {
  AgentStrategyDto,
  AssignStrategyToAgentDto
} from '../dto/agent';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';

/**
 * Service xử lý các thao tác liên quan đến strategy của agent
 */
@Injectable()
export class AgentStrategyService {
  private readonly logger = new Logger(AgentStrategyService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly agentStrategyRepository: AgentStrategyRepository,
    private readonly dataSource: DataSource,
    private readonly agentValidationService: AgentValidationService,
  ) { }

  /**
   * Lấy thông tin strategy của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin strategy của agent
   */
  async getAgentStrategy(
    agentId: string,
    userId: number
  ): Promise<AgentStrategyDto> {
    try {
      // Validate agent ownership và Strategy feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('STRATEGY')
      );

      // Lấy agent data sau khi validate
      const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!agentData) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agentUser } = agentData;

      // Nếu agent chưa có strategy
      if (!agentUser.strategyId) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_ASSIGNED);
      }

      // Lấy thông tin strategy user
      const strategyUser = await this.dataSource
        .getRepository(AgentStrategyUser)
        .createQueryBuilder('strategyUser')
        .leftJoin('agents_strategy', 'strategy', 'strategy.id = strategyUser.agents_strategy_id')
        .leftJoin('agents', 'agent', 'agent.id = strategy.id')
        .select([
          'strategyUser.id',
          'strategyUser.agentsStrategyId',
          'strategyUser.example',
          'strategy.content',
          'strategy.exampleDefault',
          'agent.name'
        ])
        .where('strategyUser.id = :strategyId', { strategyId: agentUser.strategyId })
        .andWhere('strategyUser.userId = :userId', { userId })
        .getOne();

      if (!strategyUser) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Map dữ liệu thành DTO
      const result: AgentStrategyDto = {
        id: strategyUser.agentsStrategyId || '',
        name: agentData.agent.name,
        description: 'Chiến lược tùy chỉnh của người dùng',
        steps: strategyUser.example?.map((step, index) => ({
          id: index + 1,
          stepOrder: step.stepOrder,
          editableExample: step.content,
          edited: false
        })) || []
      };

      this.logger.log(`Lấy thông tin strategy thành công cho agent ${agentId}`);
      return result;

    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin strategy cho agent ${agentId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED);
    }
  }

  /**
   * Gán strategy cho agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param dto Thông tin strategy cần gán
   */
  @Transactional()
  async assignStrategyToAgent(
    agentId: string,
    userId: number,
    dto: AssignStrategyToAgentDto
  ): Promise<void> {
    try {
      // Validate agent ownership và Strategy feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('STRATEGY')
      );

      // Lấy agent data sau khi validate
      const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!agentData) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Nếu không có strategyId, gỡ strategy hiện tại
      if (!dto.strategyId) {
        await this.agentUserRepository.updateStrategyId(agentId, userId, null);
        this.logger.log(`Gỡ strategy thành công cho agent ${agentId}`);
        return;
      }

      // Kiểm tra strategy có tồn tại không
      const strategyData = await this.agentStrategyRepository.findById(dto.strategyId);
      if (!strategyData) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Tạo hoặc cập nhật strategy user
      const existingStrategyUser = await this.dataSource
        .getRepository(AgentStrategyUser)
        .createQueryBuilder('strategyUser')
        .where('strategyUser.agentsStrategyId = :strategyId', { strategyId: dto.strategyId })
        .andWhere('strategyUser.userId = :userId', { userId })
        .getOne();

      let strategyUserId: string;

      if (existingStrategyUser) {
        strategyUserId = existingStrategyUser.id;
      } else {
        // Tạo mới strategy user
        const newStrategyUser = this.dataSource
          .getRepository(AgentStrategyUser)
          .create({
            agentsStrategyId: dto.strategyId,
            userId,
            example: strategyData.strategy.exampleDefault || [],
            ownedAt: Date.now()
          });

        const savedStrategyUser = await this.dataSource
          .getRepository(AgentStrategyUser)
          .save(newStrategyUser);

        strategyUserId = savedStrategyUser.id;
      }

      // Cập nhật strategy ID cho agent user
      await this.agentUserRepository.updateStrategyId(agentId, userId, strategyUserId);

      this.logger.log(`Gán strategy ${dto.strategyId} thành công cho agent ${agentId}`);

    } catch (error) {
      this.logger.error(`Lỗi khi gán strategy cho agent ${agentId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ASSIGN_FAILED);
    }
  }
}
