import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * Mapping các API endpoints với TypeAgent features cần validate
 */
export const AGENT_FEATURE_MAPPING = {
  // Facebook Page Integration
  FACEBOOK_PAGE: {
    features: ['enableOutputToMessenger'] as (keyof TypeAgentConfig)[],
    description: 'Tích hợp Facebook Page yêu cầu tính năng đầu ra qua Messenger'
  },

  // Website Integration  
  WEBSITE: {
    features: ['enableOutputToWebsiteLiveChat'] as (keyof TypeAgentConfig)[],
    description: 'Tích hợp Website yêu cầu tính năng đầu ra qua Live Chat Website'
  },

  // Zalo OA Integration
  ZALO_OA: {
    features: ['enableOutputToZaloOA'] as (keyof TypeAgentConfig)[],
    description: 'Tích hợp Zalo OA yêu cầu tính năng đầu ra qua Zalo OA'
  },

  // Agent Resources (URL, Media, Product)
  RESOURCES: {
    features: ['enableResourceUsage'] as (keyof TypeAgentConfig)[],
    description: 'Quản lý tài nguyên Agent yêu cầu tính năng sử dụng tài nguyên'
  },

  // Agent Strategy
  STRATEGY: {
    features: ['enableDynamicStrategyExecution'] as (keyof TypeAgentConfig)[],
    description: 'Quản lý chiến lược Agent yêu cầu tính năng thực thi chiến lược động'
  },

  // Agent Profile
  PROFILE: {
    features: ['enableAgentProfileCustomization'] as (keyof TypeAgentConfig)[],
    description: 'Quản lý hồ sơ Agent yêu cầu tính năng tùy chỉnh hồ sơ Agent'
  },

  // Conversion Tracking
  CONVERSION: {
    features: ['enableTaskConversionTracking'] as (keyof TypeAgentConfig)[],
    description: 'Theo dõi chuyển đổi yêu cầu tính năng theo dõi chuyển đổi nhiệm vụ'
  },

  // Multi-Agent Collaboration
  MULTI_AGENT: {
    features: ['enableMultiAgentCollaboration'] as (keyof TypeAgentConfig)[],
    description: 'Hợp tác đa Agent yêu cầu tính năng hợp tác đa Agent'
  },

  // Output Messenger (for general messenger features)
  OUTPUT_MESSENGER: {
    features: ['enableOutputToMessenger'] as (keyof TypeAgentConfig)[],
    description: 'Đầu ra qua Messenger yêu cầu tính năng đầu ra qua Messenger'
  },

  // Output Website (for general website features)
  OUTPUT_WEBSITE: {
    features: ['enableOutputToWebsiteLiveChat'] as (keyof TypeAgentConfig)[],
    description: 'Đầu ra qua Website yêu cầu tính năng đầu ra qua Live Chat Website'
  }
} as const;

/**
 * Type cho các feature keys
 */
export type AgentFeatureKey = keyof typeof AGENT_FEATURE_MAPPING;

/**
 * Helper function để lấy features cần validate
 * @param featureKey Key của feature
 * @returns Danh sách features cần validate
 */
export function getRequiredFeatures(featureKey: AgentFeatureKey): (keyof TypeAgentConfig)[] {
  return AGENT_FEATURE_MAPPING[featureKey].features;
}

/**
 * Helper function để lấy mô tả feature
 * @param featureKey Key của feature
 * @returns Mô tả feature
 */
export function getFeatureDescription(featureKey: AgentFeatureKey): string {
  return AGENT_FEATURE_MAPPING[featureKey].description;
}
