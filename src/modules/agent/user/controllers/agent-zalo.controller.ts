import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import { AgentZaloService } from '../services/agent-zalo.service';
import {
  AddZaloOfficialAccountsDto,
  RemoveZaloOfficialAccountsDto,
  AgentZaloOfficialAccountResponseDto,
  ZaloOfficialAccountOperationResultDto,
} from '../dto/zalo/agent-zalo.dto';
import { QueryDto } from '@/common/dto';

/**
 * Controller quản lý Zalo Official Accounts trong agent cho người dùng
 */
@ApiTags('Agent Zalo Official Accounts')
@Controller('user/agents/:agentId/zalo-official-accounts')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentZaloController {
  constructor(private readonly agentZaloService: AgentZaloService) {}

  /**
   * Thêm nhiều Zalo Official Accounts vào agent
   */
  @Post()
  @ApiOperation({
    summary: 'Thêm nhiều Zalo Official Accounts vào agent',
    description: 'Thêm nhiều Zalo Official Accounts vào agent. Chỉ có thể thêm những OA thuộc về user và đang active.',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thêm Zalo Official Accounts thành công',
    type: ApiResponseDto<ZaloOfficialAccountOperationResultDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy agent',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async addZaloOfficialAccounts(
    @CurrentUser('id') userId: number,
    @Param('agentId', ParseUUIDPipe) agentId: string,
    @Body() addDto: AddZaloOfficialAccountsDto,
  ): Promise<ApiResponseDto<ZaloOfficialAccountOperationResultDto>> {
    const result = await this.agentZaloService.addZaloOfficialAccounts(
      userId,
      agentId,
      addDto,
    );

    return ApiResponseDto.success(
      result,
      `Đã thêm ${result.successCount} Zalo Official Accounts vào agent`,
    );
  }

  /**
   * Lấy danh sách Zalo Official Accounts được gán trong agent
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách Zalo Official Accounts được gán trong agent',
    description: 'Lấy danh sách tất cả Zalo Official Accounts đã được gán cho agent với phân trang và tìm kiếm.',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    type: 'string',
    format: 'uuid',
  })
  @ApiQuery({
    name: 'page',
    description: 'Số trang',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Từ khóa tìm kiếm theo tên OA',
    required: false,
    type: String,
  })

  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách Zalo Official Accounts thành công',
    type: ApiResponseDto<AgentZaloOfficialAccountResponseDto[]>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy agent',
  })
  async getZaloOfficialAccounts(
    @CurrentUser('id') userId: number,
    @Param('agentId', ParseUUIDPipe) agentId: string,
    @Query() queryDto: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentZaloOfficialAccountResponseDto>>> {
    const result = await this.agentZaloService.getAgentZaloOfficialAccounts(
      userId,
      agentId,
      queryDto,
    );

    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách Zalo Official Accounts thành công',
    );
  }

  /**
   * Gỡ nhiều Zalo Official Accounts khỏi agent
   */
  @Delete()
  @ApiOperation({
    summary: 'Gỡ nhiều Zalo Official Accounts khỏi agent',
    description: 'Gỡ nhiều Zalo Official Accounts khỏi agent. Chỉ có thể gỡ những OA đang được gán cho agent này.',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gỡ Zalo Official Accounts thành công',
    type: ApiResponseDto<ZaloOfficialAccountOperationResultDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy agent',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async removeZaloOfficialAccounts(
    @CurrentUser('id') userId: number,
    @Param('agentId', ParseUUIDPipe) agentId: string,
    @Body() removeDto: RemoveZaloOfficialAccountsDto,
  ): Promise<ApiResponseDto<ZaloOfficialAccountOperationResultDto>> {
    const result = await this.agentZaloService.removeZaloOfficialAccounts(
      userId,
      agentId,
      removeDto,
    );

    return ApiResponseDto.success(
      result,
      `Đã gỡ ${result.successCount} Zalo Official Accounts khỏi agent`,
    );
  }
}
