import { Controller, Post, Get, Delete, Body, Param, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { ErrorCode } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentWebsiteService } from '../services/agent-website.service';
import {
  IntegrateWebsiteDto,
  IntegrateWebsitesResponseDto,
  AgentWebsiteQueryDto,
  AgentWebsiteDto
} from '../dto/website';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API liên quan đến tích hợp Website với Agent
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents/:agentId/websites')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentWebsiteController {
  constructor(private readonly agentWebsiteService: AgentWebsiteService) {}

  /**
   * API tích hợp danh sách Website vào Agent
   * @param agentId ID của Agent
   * @param dto Danh sách Website cần tích hợp
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo tích hợp thành công
   */
  @Post()
  @ApiOperation({ summary: 'Tích hợp danh sách Website vào Agent' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Tích hợp danh sách Website thành công',
    schema: ApiResponseDto.getSchema(IntegrateWebsitesResponseDto)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.WEBSITE_NOT_FOUND,
    AGENT_ERROR_CODES.WEBSITE_ALREADY_INTEGRATED,
    AGENT_ERROR_CODES.WEBSITE_INTEGRATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async integrateWebsites(
    @Param('agentId') agentId: string,
    @Body() dto: IntegrateWebsiteDto,
    @CurrentUser('id') userId: number
  ) {
    const result = await this.agentWebsiteService.integrateWebsites(agentId, userId, dto);
    return ApiResponseDto.success(result);
  }

  /**
   * API lấy danh sách Website trong Agent với phân trang
   * @param agentId ID của Agent
   * @param queryDto Tham số truy vấn và phân trang
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách Website với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách Website trong Agent với phân trang' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Website thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentWebsiteDto)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.WEBSITE_LIST_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getWebsites(
    @Param('agentId') agentId: string,
    @Query() queryDto: AgentWebsiteQueryDto,
    @CurrentUser('id') userId: number
  ) {
    const result = await this.agentWebsiteService.getWebsites(agentId, userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * API gỡ Website khỏi Agent
   * @param agentId ID của Agent
   * @param websiteId UUID của Website trong hệ thống
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo gỡ bỏ thành công
   */
  @Delete(':websiteId')
  @ApiOperation({ summary: 'Gỡ Website khỏi Agent' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiParam({
    name: 'websiteId',
    description: 'UUID của Website trong hệ thống',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Gỡ Website thành công',
    schema: ApiResponseDto.getSchema('Gỡ Website thành công')
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.WEBSITE_NOT_INTEGRATED,
    AGENT_ERROR_CODES.WEBSITE_REMOVE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeWebsite(
    @Param('agentId') agentId: string,
    @Param('websiteId') websiteId: string,
    @CurrentUser('id') userId: number
  ) {
    await this.agentWebsiteService.removeWebsite(agentId, websiteId, userId);
    return ApiResponseDto.success('Gỡ Website thành công');
  }
}
